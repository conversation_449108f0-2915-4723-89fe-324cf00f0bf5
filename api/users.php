<?php
require_once 'config/database.php';

try {
    $database = new Database();
    $database->getConnection(); // Initialize connection

    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
} catch (Exception $e) {
    error_log("Users API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    $query = "SELECT id, username, email, name, role, created_at, last_login, is_active FROM users ORDER BY created_at DESC";
    $users = $database->getMany($query);
    
    // Format users to match frontend expectations
    $formattedUsers = array_map(function($user) {
        return [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => $user['name'],
            'role' => $user['role'],
            'createdAt' => $user['created_at'],
            'lastLogin' => $user['last_login'],
            'isActive' => (bool)$user['is_active']
        ];
    }, $users);
    
    sendResponse($formattedUsers);
}

function handlePost($database) {
    $data = getRequestData();
    
    validateRequired($data, ['name', 'email', 'username', 'role']);
    
    // Generate unique ID
    $id = $database->generateId();
    
    $query = "
        INSERT INTO users (id, username, email, name, role, password, created_at, is_active)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)
    ";
    
    $params = [
        $id,
        sanitizeInput($data['username']),
        sanitizeInput(strtolower($data['email'])),
        sanitizeInput($data['name']),
        sanitizeInput($data['role']),
        sanitizeInput($data['password'] ?? 'password123'), // Default password
        ($data['isActive'] ?? true) ? 1 : 0
    ];
    
    $database->executeQuery($query, $params);
    
    sendResponse(['id' => $id]);
}

function handlePut($database) {
    $data = getRequestData();
    
    validateRequired($data, ['id', 'name', 'email', 'username', 'role']);
    
    $query = "
        UPDATE users
        SET username = ?, email = ?, name = ?, role = ?, is_active = ?
        WHERE id = ?
    ";
    
    $params = [
        sanitizeInput($data['username']),
        sanitizeInput(strtolower($data['email'])),
        sanitizeInput($data['name']),
        sanitizeInput($data['role']),
        ($data['isActive'] ?? true) ? 1 : 0,
        sanitizeInput($data['id'])
    ];
    
    // Update password if provided
    if (isset($data['password']) && !empty($data['password'])) {
        $query = "
            UPDATE users
            SET username = ?, email = ?, name = ?, role = ?, password = ?, is_active = ?
            WHERE id = ?
        ";
        $params = [
            sanitizeInput($data['username']),
            sanitizeInput(strtolower($data['email'])),
            sanitizeInput($data['name']),
            sanitizeInput($data['role']),
            sanitizeInput($data['password']),
            ($data['isActive'] ?? true) ? 1 : 0,
            sanitizeInput($data['id'])
        ];
    }
    
    $database->executeQuery($query, $params);
    
    sendResponse(['success' => true, 'message' => 'User updated successfully']);
}

function handleDelete($database) {
    $data = getRequestData();
    
    validateRequired($data, ['id']);
    
    // Soft delete - set is_active to false
    $query = "UPDATE users SET is_active = 0 WHERE id = ?";
    $database->executeQuery($query, [sanitizeInput($data['id'])]);
    
    sendResponse(['success' => true, 'message' => 'User deleted successfully']);
}
?>
